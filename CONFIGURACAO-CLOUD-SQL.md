# Configuração do Cloud SQL PostgreSQL para Evolution API

## 🎯 Opções de Conexão

Existem duas formas principais de conectar sua aplicação ao Cloud SQL:

### 1. Conexão Direta (Mais Simples)

Use o IP privado da instância Cloud SQL diretamente na string de conexão.

**Vantagens:**
- Configuração mais simples
- Menos recursos utilizados

**Desvantagens:**
- Menos seguro
- Requer configuração de rede VPC

### 2. Cloud SQL Proxy (Recomendado)

Use um container sidecar com o Cloud SQL Proxy.

**Vantagens:**
- <PERSON><PERSON> segu<PERSON> (conexão criptografada)
- Autenticação via IAM
- Não requer configuração de rede complexa

**Desvantagens:**
- Configuração mais complexa
- Recursos adicionais necessários

## 🔧 Configuração Passo a Passo

### Opção 1: Conexão Direta

1. **Obter IP Privado da Instância:**
```bash
gcloud sql instances describe NOME_DA_INSTANCIA --format="value(ipAddresses[0].ipAddress)"
```

2. **Configurar String de Conexão:**
```
postgresql://high-capital-dev:YUli3z5([ZEJ%UOZ@***********:5432/evolution?schema=public
```
Host=**************;Port=5432;Database=postgres-high-capital;Username=postgres-high-capital;Password=YUli3z5([ZEJ%UOZ

3. **Usar o deployment padrão:**
```bash
kubectl apply -f k8s/deployment.yaml
```

### Opção 2: Cloud SQL Proxy (Recomendado)

1. **Criar Service Account:**
```bash
gcloud iam service-accounts create cloud-sql-proxy \
    --display-name="Cloud SQL Proxy Service Account"
```

2. **Conceder Permissões:**
```bash
gcloud projects add-iam-policy-binding PROJECT_ID \
    --member="serviceAccount:cloud-sql-proxy@PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/cloudsql.client"
```

3. **Configurar Workload Identity:**
```bash
gcloud iam service-accounts add-iam-policy-binding \
    cloud-sql-proxy@PROJECT_ID.iam.gserviceaccount.com \
    --role roles/iam.workloadIdentityUser \
    --member "serviceAccount:PROJECT_ID.svc.id.goog[evolution-api/cloud-sql-proxy-sa]"
```

4. **Usar deployment com proxy:**
```bash
kubectl apply -f k8s/cloud-sql-proxy.yaml
```

## 🔐 Configuração de Credenciais

### Criar Usuário no PostgreSQL

```bash
# Conectar à instância
gcloud sql connect NOME_DA_INSTANCIA --user=postgres

# Criar usuário e banco
CREATE USER evolution_user WITH PASSWORD 'sua_senha_segura';
CREATE DATABASE evolution OWNER evolution_user;
GRANT ALL PRIVILEGES ON DATABASE evolution TO evolution_user;
```

### Atualizar Secret no Kubernetes

Use o script fornecido:
```bash
./scripts/update-secret.sh
```

Ou manualmente:
```bash
kubectl create secret generic evolution-api-secret \
    --namespace=evolution-api \
    --from-literal=AUTHENTICATION_API_KEY="sua-api-key" \
    --from-literal=DATABASE_CONNECTION_URI="********************************/db?schema=public"
```

## 🌐 Configuração de Rede

### Para Conexão Direta

1. **Habilitar IP Privado:**
```bash
gcloud sql instances patch NOME_DA_INSTANCIA --network=default --no-assign-ip
```

2. **Autorizar Rede do GKE:**
```bash
# Obter range de IPs do cluster
gcloud container clusters describe CLUSTER_NAME --zone=ZONE --format="value(clusterIpv4Cidr)"

# Autorizar no Cloud SQL
gcloud sql instances patch NOME_DA_INSTANCIA \
    --authorized-networks=CLUSTER_IP_RANGE
```

### Para Cloud SQL Proxy

Não é necessária configuração adicional de rede.

## 🔍 Teste de Conectividade

### Teste Manual

1. **Criar pod de teste:**
```bash
kubectl run postgres-client --rm -it --image=postgres:15 -- bash
```

2. **Testar conexão:**
```bash
psql "************************************/database"
```

### Verificar Logs da Aplicação

```bash
kubectl logs -f deployment/evolution-api -n evolution-api
```

## 🛠️ Troubleshooting

### Erro de Conexão

1. **Verificar credenciais:**
```bash
kubectl get secret evolution-api-secret -n evolution-api -o yaml
```

2. **Verificar conectividade de rede:**
```bash
kubectl exec -it deployment/evolution-api -n evolution-api -- nslookup IP_DO_CLOUD_SQL
```

3. **Verificar logs do Cloud SQL Proxy (se usando):**
```bash
kubectl logs deployment/evolution-api -c cloud-sql-proxy -n evolution-api
```

### Erro de Autenticação

1. Verificar se o usuário existe no PostgreSQL
2. Verificar se a senha está correta
3. Verificar permissões do usuário no banco

### Erro de Rede

1. Verificar se o IP privado está habilitado
2. Verificar se a rede está autorizada
3. Verificar firewall rules

## 📊 Monitoramento

### Métricas do Cloud SQL

```bash
# Ver conexões ativas
gcloud sql operations list --instance=NOME_DA_INSTANCIA

# Ver métricas
gcloud logging read "resource.type=gce_instance AND resource.labels.instance_id=INSTANCE_ID"
```

### Logs da Aplicação

```bash
# Logs em tempo real
kubectl logs -f deployment/evolution-api -n evolution-api

# Logs com filtro
kubectl logs deployment/evolution-api -n evolution-api | grep -i database
```
