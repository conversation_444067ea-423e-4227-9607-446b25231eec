# Opcional: Se você quiser usar o Cloud SQL Proxy como sidecar container
# Isso é útil para conexões mais seguras com Cloud SQL
apiVersion: apps/v1
kind: Deployment
metadata:
  name: evolution-api-with-proxy
  namespace: default
  labels:
    app: evolution-api-proxy
spec:
  replicas: 1
  selector:
    matchLabels:
      app: evolution-api-proxy
  template:
    metadata:
      labels:
        app: evolution-api-proxy
    spec:
      serviceAccountName: cloud-sql-proxy-sa  # Você precisa criar este service account
      containers:
      - name: evolution-api
        image: atendai/evolution-api:v2.1.1
        ports:
        - containerPort: 8080
          name: http
        env:
        # Secrets
        - name: AUTHENTICATION_API_KEY
          valueFrom:
            secretKeyRef:
              name: evolution-api-secret
              key: AUTHENTICATION_API_KEY
        # Para Cloud SQL Proxy, a conexão será via localhost
        - name: DATABASE_CONNECTION_URI
          value: "postgresql://high-capital-dev:YUli3z5([ZEJ%UOZ@localhost:5432/evolution?schema=public"
        # ConfigMap values (mesmo do deployment anterior)
        - name: DATABASE_ENABLED
          valueFrom:
            configMapKeyRef:
              name: evolution-api-config
              key: DATABASE_ENABLED
        # ... outras variáveis do ConfigMap
        volumeMounts:
        - name: evolution-instances
          mountPath: /evolution/instances
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      
      # Cloud SQL Proxy sidecar container
      - name: cloud-sql-proxy
        image: gcr.io/cloud-sql-connectors/cloud-sql-proxy:2.8.0
        args:
          - "--structured-logs"
          - "--port=5432"
          - "highcapital-470117:us-east1:postgres-high-capital-dev"  # Substitua pelos valores reais
        securityContext:
          runAsNonRoot: true
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
      
      volumes:
      - name: evolution-instances
        persistentVolumeClaim:
          claimName: evolution-instances-pvc
      restartPolicy: Always
