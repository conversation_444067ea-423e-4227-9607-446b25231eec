apiVersion: v1
kind: ConfigMap
metadata:
  name: evolution-api-config
  namespace: default
data:
  # Database configuration
  DATABASE_ENABLED: "true"
  DATABASE_PROVIDER: "postgresql"
  DATABASE_CONNECTION_CLIENT_NAME: "evolution_exchange"
  
  # Database save options
  DATABASE_SAVE_DATA_INSTANCE: "true"
  DATABASE_SAVE_DATA_NEW_MESSAGE: "true"
  DATABASE_SAVE_MESSAGE_UPDATE: "true"
  DATABASE_SAVE_DATA_CONTACTS: "true"
  DATABASE_SAVE_DATA_CHATS: "true"
  DATABASE_SAVE_DATA_LABELS: "true"
  DATABASE_SAVE_DATA_HISTORIC: "true"
  
  # Cache Redis configuration
  CACHE_REDIS_ENABLED: "false"
  CACHE_REDIS_URI: "redis://redis:6379/6"
  CACHE_REDIS_PREFIX_KEY: "evolution"
  CACHE_REDIS_SAVE_INSTANCES: "false"
  
  # Local cache configuration
  CACHE_LOCAL_ENABLED: "false"
