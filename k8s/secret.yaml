apiVersion: v1
kind: Secret
metadata:
  name: evolution-api-secret
  namespace: default
type: Opaque
data:
  # Base64 encoded values - você deve substituir pelos valores reais
  # Para codificar: echo -n "seu-valor" | base64
  
  # API Key (substitua pelo valor real em base64)
  AUTHENTICATION_API_KEY: ZThkZjNjNjYtZTgzNy00NjlhLTg5OWMtNDQxY2MwZGJkZTA4
  
  # Database connection URI (substitua pela URI real do Cloud SQL em base64)
  # Formato: ******************************************/database?schema=public
  DATABASE_CONNECTION_URI: cG9zdGdyZXNxbDovL2hpZ2gtY2FwaXRhbC1kZXY6WVVsaTN6NShbWkVKJVVPWkBsb2NhbGhvc3Q6NTQzMi9ldm9sdXRpb24/c2NoZW1hPXB1YmxpYw==
