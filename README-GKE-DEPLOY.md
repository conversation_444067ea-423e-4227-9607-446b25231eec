# Deploy da Evolution API no Google Kubernetes Engine (GKE)

Este guia explica como migrar sua aplicação Evolution API do Docker Compose para o Google Kubernetes Engine (GKE) com PostgreSQL do Cloud SQL.

## 📋 Pré-requisitos

- Conta no Google Cloud Platform (GCP)
- Google Cloud SDK (`gcloud`) instalado e configurado
- `kubectl` instalado
- Domínio próprio (opcional, para HTTPS)

## 🏗️ Arquitetura

```
Internet → Load Balancer → GKE Cluster → Evolution API Pod
                                      ↓
                              Cloud SQL PostgreSQL
```

## 🚀 Passo a Passo

### 1. Configuração Inicial do GCP

```bash
# Execute o script de configuração
./scripts/setup-gcp.sh
```

**Antes de executar, edite as variáveis no script:**
- `PROJECT_ID`: ID do seu projeto GCP
- `CLUSTER_NAME`: Nome do cluster GKE
- `ZONE`: Zona onde criar o cluster
- `DB_INSTANCE_NAME`: Nome da instância Cloud SQL
- `DOMAIN`: Seu domínio (se tiver)

### 2. Configuração das Credenciais

Atualize o arquivo `k8s/secret.yaml` ou use o script:

```bash
./scripts/update-secret.sh
```

Você precisará fornecer:
- API Key da Evolution API
- Credenciais do banco PostgreSQL
- IP da instância Cloud SQL

### 3. Deploy da Aplicação

```bash
./scripts/deploy.sh
```

### 4. Monitoramento

```bash
./scripts/monitor.sh
```

## 📁 Estrutura dos Arquivos

```
k8s/
├── namespace.yaml              # Namespace da aplicação
├── configmap.yaml             # Configurações não-sensíveis
├── secret.yaml                # Credenciais e dados sensíveis
├── persistent-volume.yaml     # Volume para dados persistentes
├── deployment.yaml            # Deployment principal
├── service.yaml               # Service interno
├── ingress.yaml               # Ingress para acesso externo
├── managed-certificate.yaml   # Certificado SSL automático
├── cloud-sql-proxy.yaml       # Deployment com Cloud SQL Proxy (opcional)
└── service-account.yaml       # Service Account para Cloud SQL Proxy

scripts/
├── setup-gcp.sh              # Configuração inicial do GCP
├── deploy.sh                 # Deploy da aplicação
├── update-secret.sh          # Atualização de credenciais
└── monitor.sh                # Monitoramento da aplicação
```

## 🔧 Configurações Importantes

### Cloud SQL

1. **Conexão Direta**: Use o IP privado da instância Cloud SQL
2. **Cloud SQL Proxy**: Use o deployment alternativo com proxy (mais seguro)

### Domínio e SSL

1. Configure seu DNS para apontar para o IP estático criado
2. O certificado SSL será criado automaticamente pelo Google
3. Aguarde alguns minutos para propagação

### Recursos

- **CPU**: 250m (request) / 500m (limit)
- **Memória**: 512Mi (request) / 1Gi (limit)
- **Storage**: 10Gi para volumes persistentes

## 🔍 Comandos Úteis

### Verificar Status
```bash
kubectl get all -n evolution-api
```

### Ver Logs
```bash
kubectl logs -f deployment/evolution-api -n evolution-api
```

### Acessar Pod
```bash
kubectl exec -it deployment/evolution-api -n evolution-api -- /bin/bash
```

### Reiniciar Deployment
```bash
kubectl rollout restart deployment/evolution-api -n evolution-api
```

## 🛠️ Troubleshooting

### Pod não inicia
1. Verifique os logs: `kubectl logs pod-name -n evolution-api`
2. Verifique as configurações do secret
3. Teste a conexão com o banco

### Certificado SSL não funciona
1. Verifique se o DNS está configurado corretamente
2. Aguarde até 60 minutos para propagação
3. Verifique: `kubectl describe managedcertificate -n evolution-api`

### Banco de dados não conecta
1. Verifique as credenciais no secret
2. Teste conectividade com o Cloud SQL
3. Considere usar Cloud SQL Proxy

## 💰 Custos Estimados

- **GKE Cluster**: ~$70/mês (2 nodes e2-medium)
- **Cloud SQL**: ~$25/mês (db-f1-micro)
- **Load Balancer**: ~$18/mês
- **Storage**: ~$1/mês (10GB)

**Total estimado**: ~$114/mês

## 🔒 Segurança

- Secrets são armazenados de forma segura no Kubernetes
- Comunicação criptografada com TLS
- Rede privada entre pods e Cloud SQL
- Workload Identity para autenticação segura

## 📈 Escalabilidade

Para escalar a aplicação:

```bash
kubectl scale deployment evolution-api --replicas=3 -n evolution-api
```

Ou configure auto-scaling:

```bash
kubectl autoscale deployment evolution-api --cpu-percent=70 --min=1 --max=5 -n evolution-api
```

## 🔄 Atualizações

Para atualizar a imagem da aplicação:

```bash
kubectl set image deployment/evolution-api evolution-api=atendai/evolution-api:v2.1.2 -n evolution-api
```

## 📞 Suporte

Para dúvidas ou problemas:
1. Verifique os logs da aplicação
2. Use o script de monitoramento
3. Consulte a documentação do GKE
4. Verifique a documentação da Evolution API
