#!/bin/bash

# Script para fazer deploy da Evolution API no GKE
set -e

# ========================================
# CONFIGURAÇÕES
# ========================================

NAMESPACE="default"
K8S_DIR="../k8s"

# ========================================
# FUNÇÕES
# ========================================

print_step() {
    echo "========================================="
    echo "PASSO: $1"
    echo "========================================="
}

check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        echo "kubectl não encontrado. Instale o kubectl primeiro."
        exit 1
    fi
}

check_connection() {
    if ! kubectl cluster-info &> /dev/null; then
        echo "Não foi possível conectar ao cluster Kubernetes."
        echo "Execute: gcloud container clusters get-credentials CLUSTER_NAME --zone=ZONE"
        exit 1
    fi
}

# ========================================
# VALIDAÇÕES
# ========================================

print_step "Validando pré-requisitos"
check_kubectl
check_connection

# ========================================
# DEPLOY
# ========================================

# print_step "Criando namespace"
# kubectl apply -f $K8S_DIR/namespace.yaml

print_step "Aplicando ConfigMap"
kubectl apply -f $K8S_DIR/configmap.yaml

print_step "Aplicando Secret"
echo "ATENÇÃO: Certifique-se de que o secret.yaml contém as credenciais corretas!"
read -p "Pressione Enter para continuar ou Ctrl+C para cancelar..."
kubectl apply -f $K8S_DIR/secret.yaml

print_step "Criando PersistentVolumeClaim"
kubectl apply -f $K8S_DIR/persistent-volume.yaml

print_step "Aplicando Service Account (se usar Cloud SQL Proxy)"
kubectl apply -f $K8S_DIR/service-account.yaml

print_step "Criando Deployment"
kubectl apply -f $K8S_DIR/deployment.yaml

print_step "Criando Service"
kubectl apply -f $K8S_DIR/service.yaml

# ========================================
# VERIFICAÇÕES
# ========================================

print_step "Verificando status do deployment"
kubectl rollout status deployment/evolution-api -n $NAMESPACE

print_step "Listando pods"
kubectl get pods -n $NAMESPACE

print_step "Verificando services"
kubectl get services -n $NAMESPACE

# ========================================
# INFORMAÇÕES FINAIS
# ========================================

print_step "DEPLOY CONCLUÍDO"
echo ""
echo "Para monitorar os logs:"
echo "kubectl logs -f deployment/evolution-api -n $NAMESPACE"
echo ""
echo "Para verificar o status:"
echo "kubectl get all -n $NAMESPACE"
echo ""
echo "Para acessar o pod:"
echo "kubectl exec -it deployment/evolution-api -n $NAMESPACE -- /bin/bash"
