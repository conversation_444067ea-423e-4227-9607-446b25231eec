#!/bin/bash

# Script para atualizar o secret com as credenciais corretas
set -e

# ========================================
# CONFIGURAÇÕES
# ========================================

NAMESPACE="evolution-api"
SECRET_NAME="evolution-api-secret"

# ========================================
# FUNÇÕES
# ========================================

print_step() {
    echo "========================================="
    echo "$1"
    echo "========================================="
}

encode_base64() {
    echo -n "$1" | base64
}

# ========================================
# COLETA DE INFORMAÇÕES
# ========================================

print_step "CONFIGURAÇÃO DO SECRET"

echo "Digite a API Key da Evolution API:"
read -s API_KEY

echo "Digite o usuário do banco PostgreSQL:"
read DB_USER

echo "Digite a senha do banco PostgreSQL:"
read -s DB_PASSWORD

echo "Digite o IP privado da instância Cloud SQL:"
read DB_HOST

echo "Digite o nome do banco de dados:"
read DB_NAME

# ========================================
# CRIAÇÃO DA URI DE CONEXÃO
# ========================================

DB_URI="postgresql://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:5432/${DB_NAME}?schema=public"

print_step "CODIFICANDO VALORES EM BASE64"

API_KEY_B64=$(encode_base64 "$API_KEY")
DB_URI_B64=$(encode_base64 "$DB_URI")

echo "API Key (base64): $API_KEY_B64"
echo "Database URI (base64): $DB_URI_B64"

# ========================================
# ATUALIZAÇÃO DO SECRET
# ========================================

print_step "ATUALIZANDO SECRET NO KUBERNETES"

kubectl create secret generic $SECRET_NAME \
    --namespace=$NAMESPACE \
    --from-literal=AUTHENTICATION_API_KEY="$API_KEY" \
    --from-literal=DATABASE_CONNECTION_URI="$DB_URI" \
    --dry-run=client -o yaml | kubectl apply -f -

echo "Secret atualizado com sucesso!"

# ========================================
# REINICIAR DEPLOYMENT
# ========================================

print_step "REINICIANDO DEPLOYMENT"

kubectl rollout restart deployment/evolution-api -n $NAMESPACE
kubectl rollout status deployment/evolution-api -n $NAMESPACE

echo "Deployment reiniciado com sucesso!"
