#!/bin/bash

# Script para monitorar a aplicação Evolution API no GKE
set -e

NAMESPACE="default"

# ========================================
# FUNÇÕES
# ========================================

print_step() {
    echo "========================================="
    echo "$1"
    echo "========================================="
}

show_status() {
    print_step "STATUS GERAL"
    kubectl get all -n $NAMESPACE
}

show_logs() {
    print_step "LOGS DA APLICAÇÃO"
    kubectl logs -f deployment/evolution-api -n $NAMESPACE --tail=50
}

show_events() {
    print_step "EVENTOS DO NAMESPACE"
    kubectl get events -n $NAMESPACE --sort-by='.lastTimestamp'
}

show_ingress_info() {
    print_step "INFORMAÇÕES DO INGRESS"
    kubectl get ingress -n $NAMESPACE -o wide
    kubectl describe ingress evolution-api-ingress -n $NAMESPACE
}

show_certificate_status() {
    print_step "STATUS DO CERTIFICADO SSL"
    kubectl get managedcertificate -n $NAMESPACE
    kubectl describe managedcertificate evolution-api-ssl -n $NAMESPACE
}

# ========================================
# MENU PRINCIPAL
# ========================================

while true; do
    echo ""
    echo "========================================="
    echo "MONITOR EVOLUTION API - GKE"
    echo "========================================="
    echo "1. Status geral"
    echo "2. Ver logs em tempo real"
    echo "3. Ver eventos"
    echo "4. Informações do Ingress"
    echo "5. Status do certificado SSL"
    echo "6. Executar comando personalizado"
    echo "7. Sair"
    echo ""
    read -p "Escolha uma opção: " choice

    case $choice in
        1)
            show_status
            ;;
        2)
            show_logs
            ;;
        3)
            show_events
            ;;
        4)
            show_ingress_info
            ;;
        5)
            show_certificate_status
            ;;
        6)
            echo "Digite o comando kubectl (sem 'kubectl'):"
            read custom_command
            kubectl $custom_command
            ;;
        7)
            echo "Saindo..."
            exit 0
            ;;
        *)
            echo "Opção inválida!"
            ;;
    esac
    
    echo ""
    read -p "Pressione Enter para continuar..."
done
