#!/bin/bash

# Script para configurar o ambiente GCP para Evolution API
# Execute este script após configurar as variáveis abaixo

set -e

# ========================================
# CONFIGURAÇÕES - EDITE ESTAS VARIÁVEIS
# ========================================

PROJECT_ID="seu-project-id"
CLUSTER_NAME="evolution-cluster"
ZONE="us-central1-a"
REGION="us-central1"
DB_INSTANCE_NAME="evolution-db"
DB_NAME="evolution"
DB_USER="evolution_user"
DOMAIN="evolution-api.seu-dominio.com"

# ========================================
# FUNÇÕES
# ========================================

print_step() {
    echo "========================================="
    echo "PASSO: $1"
    echo "========================================="
}

# ========================================
# CONFIGURAÇÃO INICIAL
# ========================================

print_step "Configurando projeto GCP"
gcloud config set project $PROJECT_ID

print_step "Habilitando APIs necessárias"
gcloud services enable container.googleapis.com
gcloud services enable sqladmin.googleapis.com
gcloud services enable compute.googleapis.com

# ========================================
# CRIAÇÃO DO CLUSTER GKE
# ========================================

print_step "Criando cluster GKE"
gcloud container clusters create $CLUSTER_NAME \
    --zone=$ZONE \
    --num-nodes=2 \
    --machine-type=e2-medium \
    --enable-autorepair \
    --enable-autoupgrade \
    --enable-autoscaling \
    --min-nodes=1 \
    --max-nodes=5 \
    --workload-pool=$PROJECT_ID.svc.id.goog

print_step "Configurando kubectl"
gcloud container clusters get-credentials $CLUSTER_NAME --zone=$ZONE

# ========================================
# CONFIGURAÇÃO DO CLOUD SQL
# ========================================

print_step "Criando instância Cloud SQL PostgreSQL"
gcloud sql instances create $DB_INSTANCE_NAME \
    --database-version=POSTGRES_15 \
    --tier=db-f1-micro \
    --region=$REGION \
    --storage-type=SSD \
    --storage-size=10GB \
    --storage-auto-increase

print_step "Criando banco de dados"
gcloud sql databases create $DB_NAME --instance=$DB_INSTANCE_NAME

print_step "Criando usuário do banco"
echo "Digite a senha para o usuário $DB_USER:"
gcloud sql users create $DB_USER --instance=$DB_INSTANCE_NAME --password

# ========================================
# CONFIGURAÇÃO DE REDE
# ========================================

print_step "Reservando IP estático"
gcloud compute addresses create evolution-api-ip --global

print_step "Obtendo IP estático"
STATIC_IP=$(gcloud compute addresses describe evolution-api-ip --global --format="value(address)")
echo "IP estático criado: $STATIC_IP"
echo "Configure seu DNS para apontar $DOMAIN para $STATIC_IP"

# ========================================
# SERVICE ACCOUNT PARA CLOUD SQL PROXY (OPCIONAL)
# ========================================

print_step "Criando Service Account para Cloud SQL Proxy"
gcloud iam service-accounts create cloud-sql-proxy \
    --display-name="Cloud SQL Proxy Service Account"

print_step "Concedendo permissões ao Service Account"
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:cloud-sql-proxy@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/cloudsql.client"

print_step "Configurando Workload Identity"
gcloud iam service-accounts add-iam-policy-binding \
    cloud-sql-proxy@$PROJECT_ID.iam.gserviceaccount.com \
    --role roles/iam.workloadIdentityUser \
    --member "serviceAccount:$PROJECT_ID.svc.id.goog[evolution-api/cloud-sql-proxy-sa]"

# ========================================
# INFORMAÇÕES FINAIS
# ========================================

print_step "CONFIGURAÇÃO CONCLUÍDA"
echo ""
echo "Próximos passos:"
echo "1. Configure seu DNS para apontar $DOMAIN para $STATIC_IP"
echo "2. Edite os arquivos YAML em k8s/ com as informações corretas:"
echo "   - PROJECT_ID: $PROJECT_ID"
echo "   - DB_INSTANCE_NAME: $DB_INSTANCE_NAME"
echo "   - DOMAIN: $DOMAIN"
echo "3. Atualize o secret.yaml com as credenciais corretas do banco"
echo "4. Execute: ./scripts/deploy.sh"
echo ""
echo "Informações importantes:"
echo "- IP estático: $STATIC_IP"
echo "- Instância Cloud SQL: $DB_INSTANCE_NAME"
echo "- Cluster GKE: $CLUSTER_NAME"
